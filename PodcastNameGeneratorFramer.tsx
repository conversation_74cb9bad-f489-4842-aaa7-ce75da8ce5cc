import React, { useState } from 'react';

interface PodcastName {
  name: string;
  description: string;
  domainStatus?: 'checking' | 'available' | 'taken' | 'error';
  suggestedDomain?: string;
  availableDomains?: string[]; // Array of available domain names (without .com)
  domainSuggestions?: string[]; // Array of all 4 domain suggestions from AI
}

interface NameFeedback {
  name: string;
  description: string;
  liked: boolean | null; // true = liked, false = disliked, null = no feedback
  timestamp: number;
  index: number; // Track position in results for UI state
}

interface UserPreferences {
  likedNames: NameFeedback[];
  dislikedNames: NameFeedback[];
  patterns: {
    preferredLength: 'short' | 'medium' | 'long' | null;
    preferredStyle: 'descriptive' | 'creative' | 'professional' | 'playful' | null;
    likedKeywords: string[];
    dislikedKeywords: string[];
    preferredStructure: 'single-word' | 'phrase' | 'question' | 'mixed' | null;
  };

}

interface ApiResponse {
  podcast_names: PodcastName[];
}

interface PodcastNameGeneratorProps {
  className?: string;
  style?: React.CSSProperties;
}

interface PodcastName {
  name: string;
  description: string;
  domainStatus?: 'checking' | 'available' | 'taken' | 'error';
  suggestedDomain?: string;
  availableDomains?: string[];
}

interface NameFeedback {
  name: string;
  description: string;
  liked: boolean | null;
  timestamp: number;
  index: number;
}

interface UserPreferences {
  likedNames: NameFeedback[];
  dislikedNames: NameFeedback[];
  patterns: {
    preferredLength: 'short' | 'medium' | 'long' | null;
    preferredStyle: 'descriptive' | 'creative' | 'professional' | 'playful' | null;
    likedKeywords: string[];
    dislikedKeywords: string[];
    preferredStructure: 'single-word' | 'phrase' | 'question' | 'mixed' | null;
  };
}

interface ApiResponse {
  podcast_names: PodcastName[];
}

interface PodcastNameGeneratorProps {
  className?: string;
  style?: React.CSSProperties;
}

function PodcastNameGenerator({
  className = '',
  style = {}
}: PodcastNameGeneratorProps) {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<PodcastName[]>([]);
  const [favorites, setFavorites] = useState<PodcastName[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [feedback, setFeedback] = useState<NameFeedback[]>([]);
  const [preferences, setPreferences] = useState<UserPreferences>({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null,
    },
  });
  const [showRefinementButton, setShowRefinementButton] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [hasProvidedFeedback, setHasProvidedFeedback] = useState(false);
  const [domainChecking, setDomainChecking] = useState<Set<number>>(new Set());
  const [domainRetryStatus, setDomainRetryStatus] = useState<Map<number, string>>(new Map());

  // Domain retry configuration
  const DOMAIN_RETRY_CONFIG = {
    maxRetries: 3,
    domainsPerRetry: 10,
    enableRetryLogic: true
  };
  const [pendingReplacements, setPendingReplacements] = useState<Set<number>>(new Set());
  const [flyingToFavorites, setFlyingToFavorites] = useState<Set<number>>(new Set());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [consecutiveDislikesCount, setConsecutiveDislikesCount] = useState(0);
  const [showDislikePopup, setShowDislikePopup] = useState(false);
  const [_usageCount, setUsageCount] = useState(0);
  const [isLimitReached, setIsLimitReached] = useState(false);
  const USAGE_LIMIT = 100;

  // Domain Availability Functions with Best Practices
  const generateDomainName = (podcastName: string): string => {
    // Clean the name first
    let cleanName = podcastName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize spaces
      .replace(/^the\s+/, '') // Remove "the" prefix
      .trim();

    // Split into words and filter out common stop words
    const stopWords = ['the', 'and', 'for', 'with', 'from', 'show', 'podcast', 'cast', 'of', 'in', 'on', 'at', 'to', 'a', 'an'];
    const allWords = cleanName.split(' ');
    const meaningfulWords = allWords.filter(word =>
      word.length > 2 && !stopWords.includes(word)
    );

    // Strategy 1: Single meaningful word (6-15 chars)
    if (meaningfulWords.length === 1) {
      const word = meaningfulWords[0];
      if (word.length >= 6 && word.length <= 15) {
        return word;
      }
      // If too short, add context
      if (word.length < 6) {
        return word + 'pod';
      }
      // If too long, try natural shortening
      return getNaturalShortening(word);
    }

    // Strategy 2: Two meaningful words combined (aim for 6-15 chars)
    if (meaningfulWords.length >= 2) {
      const word1 = meaningfulWords[0];
      const word2 = meaningfulWords[1];

      // Try direct combination first
      const combined = word1 + word2;
      if (combined.length >= 6 && combined.length <= 15) {
        return combined;
      }

      // Try with natural shortenings
      const short1 = getNaturalShortening(word1);
      const short2 = getNaturalShortening(word2);
      const shortCombined = short1 + short2;

      if (shortCombined.length >= 6 && shortCombined.length <= 15) {
        return shortCombined;
      }

      // Use hyphen for readability if still too long
      if (shortCombined.length > 15) {
        return short1 + '-' + short2;
      }
    }

    // Strategy 3: Use first meaningful word + context
    if (meaningfulWords.length > 0) {
      const mainWord = getNaturalShortening(meaningfulWords[0]);

      // Try different suffixes
      const suffixes = ['cast', 'pod', 'show', 'talk'];
      for (const suffix of suffixes) {
        const candidate = mainWord + suffix;
        if (candidate.length >= 6 && candidate.length <= 15) {
          return candidate;
        }
      }

      // Return just the main word if it's good length
      if (mainWord.length >= 6 && mainWord.length <= 15) {
        return mainWord;
      }
    }

    // Fallback: Use first word from original
    const firstWord = allWords[0];
    if (firstWord && firstWord.length >= 3) {
      const shortened = getNaturalShortening(firstWord);
      return shortened + 'pod';
    }

    // Ultimate fallback
    return 'podcast' + Math.random().toString(36).substring(2, 5);
  };
  // Scroll preservation utility
  const preserveScrollPosition = (callback: () => void) => {
    const beforeScrollY = window.scrollY;
    const beforeDocumentHeight = document.documentElement.scrollHeight;
    const beforeViewportHeight = window.innerHeight;
    const beforeScrollFromBottom = beforeDocumentHeight - beforeScrollY - beforeViewportHeight;

    callback();

    requestAnimationFrame(() => {
      const afterDocumentHeight = document.documentElement.scrollHeight;
      const afterViewportHeight = window.innerHeight;
      const newScrollY = afterDocumentHeight - beforeScrollFromBottom - afterViewportHeight;

      if (Math.abs(afterDocumentHeight - beforeDocumentHeight) > 5) {
        window.scrollTo(0, Math.max(0, newScrollY));
      } else {
        window.scrollTo(0, beforeScrollY);
      }
    });
  };

  // Usage tracking functions
  const getUsageKey = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Usage tracking', 2, 2);
    const fingerprint = canvas.toDataURL();

    const userAgent = navigator.userAgent;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const combined = fingerprint + userAgent + language + timezone;
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }

    return `podcast_usage_${Math.abs(hash)}`;
  };

  const checkUsageLimit = (): boolean => {
    const usageKey = getUsageKey();
    const today = new Date().toDateString();
    const storageKey = `${usageKey}_${today}`;

    const stored = localStorage.getItem(storageKey);
    const currentCount = stored ? parseInt(stored, 10) : 0;

    setUsageCount(currentCount);

    if (currentCount >= USAGE_LIMIT) {
      setIsLimitReached(true);
      return false;
    }

    return true;
  };

  const incrementUsageCount = (count: number = 1) => {
    const usageKey = getUsageKey();
    const today = new Date().toDateString();
    const storageKey = `${usageKey}_${today}`;

    const stored = localStorage.getItem(storageKey);
    const currentCount = stored ? parseInt(stored, 10) : 0;
    const newCount = currentCount + count;

    localStorage.setItem(storageKey, newCount.toString());
    setUsageCount(newCount);

    if (newCount >= USAGE_LIMIT) {
      setIsLimitReached(true);
    }
  };

  // Initialize usage tracking on component mount
  React.useEffect(() => {
    checkUsageLimit();
  }, []);

  // Domain generation and checking functions

  const getNaturalShortening = (word: string): string => {
    if (word.length <= 8) return word;

    const naturalShortenings: { [key: string]: string } = {
      'business': 'biz',
      'entrepreneur': 'entre',
      'marketing': 'market',
      'technology': 'tech',
      'development': 'dev',
      'stories': 'story',
      'lifestyle': 'life',
      'wellness': 'well',
      'fitness': 'fit',
      'health': 'heal',
    };

    if (naturalShortenings[word]) {
      return naturalShortenings[word];
    }

    if (word.length > 8) {
      return word.substring(0, 8);
    }

    return word;
  };

  const checkDomainAvailability = async (domain: string): Promise<'available' | 'taken' | 'error'> => {
    try {
      const response = await fetch(`https://dns.google/resolve?name=${domain}&type=A`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        return 'error';
      }

      const data = await response.json();

      if (data.Answer && data.Answer.length > 0) {
        return 'taken';
      }

      return 'available';
    } catch (error) {
      console.warn('Domain check failed:', error);
      return 'error';
    }
  };

  // Clean domain name by removing multiple .com extensions
  const cleanDomainName = (domain: string): string => {
    if (!domain) return domain;

    let cleanDomain = domain.toLowerCase();

    // Remove .com extensions iteratively until none remain
    while (cleanDomain.endsWith('.com')) {
      cleanDomain = cleanDomain.slice(0, -4);
    }

    // Remove other common extensions
    cleanDomain = cleanDomain.replace(/\.(net|org|io|co)$/g, '');

    // Remove any trailing dots
    cleanDomain = cleanDomain.replace(/\.+$/g, '');

    return cleanDomain;
  };



  // Generate fallback domain patterns
  const generateFallbackDomains = (podcastName: string, existingDomains: string[] = []): string[] => {
    const baseDomain = generateDomainName(podcastName);
    const fallbackDomains: string[] = [];

    // Generate various fallback patterns
    const patterns = [
      `${baseDomain}podcast`,
      `${baseDomain}show`,
      `${baseDomain}cast`,
      `${baseDomain}pod`,
      `the${baseDomain}`,
      `${baseDomain}hq`,
      `${baseDomain}fm`,
      `${baseDomain}audio`,
      `${baseDomain}talk`,
      `${baseDomain}weekly`,
      `${baseDomain}daily`,
      `${baseDomain}live`,
      `${baseDomain}network`,
      `${baseDomain}media`,
      `${baseDomain}studio`
    ];

    // Add numbered variations
    for (let i = 1; i <= 10; i++) {
      patterns.push(`${baseDomain}${i}`);
    }

    // Filter out existing domains and limit results
    const existingSet = new Set(existingDomains.map(d => d.toLowerCase()));

    for (const pattern of patterns) {
      if (!existingSet.has(pattern.toLowerCase()) && fallbackDomains.length < 20) {
        fallbackDomains.push(pattern);
        existingSet.add(pattern.toLowerCase());
      }
    }

    return fallbackDomains;
  };

  // Check multiple domains with retry logic
  const checkMultipleDomainsWithRetry = async (
    domainSuggestions: string[],
    podcastName: string,
    maxRetries: number = 3,
    resultIndex?: number
  ): Promise<string[]> => {
    let availableDomains: string[] = [];
    let allTestedDomains: string[] = [];
    let currentSuggestions = [...domainSuggestions];

    const updateRetryStatus = (status: string) => {
      if (resultIndex !== undefined) {
        setDomainRetryStatus(prev => new Map(prev.set(resultIndex, status)));
      }
    };

    for (let retry = 0; retry <= maxRetries && availableDomains.length < 4; retry++) {
      const statusMessage = retry === 0
        ? `Checking ${currentSuggestions.length} domain suggestions...`
        : `Retry ${retry}/${maxRetries}: Checking ${currentSuggestions.length} fallback domains...`;

      console.log(`🔄 Domain check attempt ${retry + 1}/${maxRetries + 1} for "${podcastName}"`);
      updateRetryStatus(statusMessage);

      // Check current batch of suggestions
      for (const domain of currentSuggestions) {
        const cleanedDomain = cleanDomainName(domain);
        const fullDomain = `${cleanedDomain}.com`;

        // Skip if already tested
        if (allTestedDomains.includes(cleanedDomain)) {
          continue;
        }

        allTestedDomains.push(cleanedDomain);

        try {
          const status = await checkDomainAvailability(fullDomain);
          if (status === 'available') {
            availableDomains.push(cleanedDomain);
            console.log(`✅ Found available domain: ${fullDomain}`);
            updateRetryStatus(`Found ${availableDomains.length} available domain${availableDomains.length > 1 ? 's' : ''}!`);

            // Stop if we have 4 available domains
            if (availableDomains.length >= 4) {
              console.log(`🎯 Found maximum 4 domains, stopping search`);
              break;
            }
          } else {
            console.log(`❌ Domain taken: ${fullDomain}`);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to check domain ${fullDomain}:`, error);
        }
      }

      // If we have enough domains, stop retrying
      if (availableDomains.length >= 4) {
        console.log(`🎯 Collected maximum 4 domains, stopping retries`);
        break;
      }

      // If no available domains found and we have retries left, generate fallback domains
      if (availableDomains.length === 0 && retry < maxRetries) {
        console.log(`🔄 No available domains found, generating fallback domains (retry ${retry + 1})`);
        updateRetryStatus(`Generating fallback domains (attempt ${retry + 2}/${maxRetries + 1})...`);

        const fallbackDomains = generateFallbackDomains(podcastName, allTestedDomains);
        currentSuggestions = fallbackDomains.slice(0, DOMAIN_RETRY_CONFIG.domainsPerRetry);

        if (currentSuggestions.length === 0) {
          console.warn(`⚠️ No more fallback domains to generate for "${podcastName}"`);
          updateRetryStatus('No more domain variations available');
          break;
        }
      }
    }

    // Clear retry status when done
    if (resultIndex !== undefined) {
      setDomainRetryStatus(prev => {
        const newMap = new Map(prev);
        newMap.delete(resultIndex);
        return newMap;
      });
    }

    console.log(`🏁 Final result for "${podcastName}": ${availableDomains.length} available domains found`);
    return availableDomains.slice(0, 4); // Ensure we never return more than 4 domains
  };

  const checkDomainsForResults = async (results: PodcastName[]) => {
    const updatedResults = [...results];

    // Initialize domain checking state for all results
    for (let i = 0; i < updatedResults.length; i++) {
      updatedResults[i].domainStatus = 'checking';
      updatedResults[i].availableDomains = [];
      setDomainChecking(prev => new Set([...prev, i]));
    }

    setResults(updatedResults);

    // Check domains for each result using retry logic
    for (let i = 0; i < updatedResults.length; i++) {
      try {
        // Generate 4 domain suggestions for this podcast name
        const baseDomain = generateDomainName(updatedResults[i].name);
        const domainSuggestions = [
          baseDomain,
          `${baseDomain}show`,
          `${baseDomain}cast`,
          `${baseDomain}pod`
        ];

        // Use retry logic to ensure we find at least one available domain
        const availableDomains = DOMAIN_RETRY_CONFIG.enableRetryLogic
          ? await checkMultipleDomainsWithRetry(domainSuggestions, updatedResults[i].name, DOMAIN_RETRY_CONFIG.maxRetries, i)
          : await checkMultipleDomainsWithRetry(domainSuggestions, updatedResults[i].name, 0, i); // No retries if disabled

        // Update the result with available domains
        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].availableDomains = availableDomains;
            newResults[i].domainStatus = availableDomains.length > 0 ? 'available' : 'taken';
            newResults[i].suggestedDomain = availableDomains.length > 0 ? availableDomains[0] : baseDomain;
          }
          return newResults;
        });

      } catch (error) {
        console.error(`Error checking domains for "${updatedResults[i].name}":`, error);
        setResults(prevResults => {
          const newResults = [...prevResults];
          if (newResults[i]) {
            newResults[i].domainStatus = 'error';
            newResults[i].availableDomains = [];
          }
          return newResults;
        });
      } finally {
        setDomainChecking(prev => {
          const newSet = new Set(prev);
          newSet.delete(i);
          return newSet;
        });
      }
    }
  };

  // Learning algorithm functions
  const analyzeLength = (liked: NameFeedback[], disliked: NameFeedback[]): 'short' | 'medium' | 'long' | null => {
    if (liked.length === 0) return null;

    const likedLengths = liked.map(n => n.name.split(' ').length);
    const dislikedLengths = disliked.map(n => n.name.split(' ').length);
    const avgLikedLength = likedLengths.reduce((a, b) => a + b, 0) / likedLengths.length;

    const avgDislikedLength = dislikedLengths.length > 0
      ? dislikedLengths.reduce((a, b) => a + b, 0) / dislikedLengths.length
      : 0;

    if (avgLikedLength <= 2 && avgDislikedLength > 2) return 'short';
    if (avgLikedLength <= 4 && (avgDislikedLength <= 2 || avgDislikedLength > 4)) return 'medium';
    if (avgLikedLength > 4 && avgDislikedLength <= 4) return 'long';

    if (avgLikedLength <= 2) return 'short';
    if (avgLikedLength <= 4) return 'medium';
    return 'long';
  };

  const analyzeStyle = (liked: NameFeedback[], disliked: NameFeedback[]): 'descriptive' | 'creative' | 'professional' | 'playful' | null => {
    if (liked.length === 0) return null;

    const likedDescriptions = liked.map(n => n.description.toLowerCase()).join(' ');
    const dislikedDescriptions = disliked.map(n => n.description.toLowerCase()).join(' ');

    if (likedDescriptions.includes('professional') || likedDescriptions.includes('business')) {
      if (!dislikedDescriptions.includes('professional') && !dislikedDescriptions.includes('business')) {
        return 'professional';
      }
    }
    if (likedDescriptions.includes('creative') || likedDescriptions.includes('unique')) {
      if (!dislikedDescriptions.includes('creative') && !dislikedDescriptions.includes('unique')) {
        return 'creative';
      }
    }
    if (likedDescriptions.includes('fun') || likedDescriptions.includes('playful')) {
      if (!dislikedDescriptions.includes('fun') && !dislikedDescriptions.includes('playful')) {
        return 'playful';
      }
    }
    return 'descriptive';
  };

  const extractKeywords = (feedback: NameFeedback[]): string[] => {
    const keywords: string[] = [];
    feedback.forEach(f => {
      const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
      const nameWords = f.name.toLowerCase().split(/\s+/).filter(word =>
        word.length > 2 && !commonWords.includes(word)
      );
      keywords.push(...nameWords);
    });

    const frequency: { [key: string]: number } = {};
    keywords.forEach(word => frequency[word] = (frequency[word] || 0) + 1);

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  };

  const analyzePreferences = (feedback: NameFeedback[]): UserPreferences['patterns'] => {
    const liked = feedback.filter(f => f.liked === true);
    const disliked = feedback.filter(f => f.liked === false);

    return {
      preferredLength: analyzeLength(liked, disliked),
      preferredStyle: analyzeStyle(liked, disliked),
      likedKeywords: extractKeywords(liked),
      dislikedKeywords: extractKeywords(disliked),
      preferredStructure: null,
    };
  };

  const generateAdaptivePrompt = (input: string, preferences: UserPreferences): string => {
    const existingNames = [
      ...preferences.likedNames.map(n => n.name.toLowerCase()),
      ...preferences.dislikedNames.map(n => n.name.toLowerCase()),
      ...results.map(r => r.name.toLowerCase())
    ];

    const basePrompt = `Create 4 unique, high-converting podcast names for: ${input}`;

    let qualityInstructions = `

COMPREHENSIVE PODCAST NAMING GUIDELINES:

CORE PRINCIPLES:
1. MEMORABILITY: Names should be easy to remember, spell, and pronounce
2. BRANDABILITY: Names should work as a brand and be trademarkable
3. SEARCHABILITY: Names should be discoverable and SEO-friendly
4. CLARITY: Names should clearly communicate the podcast's value proposition
5. UNIQUENESS: Names must be completely distinct from existing podcasts

STRUCTURAL REQUIREMENTS:
• Length: 2-4 words maximum (shorter is generally better)
• Pronunciation: Easy to say and understand when spoken aloud
• Spelling: Intuitive spelling, avoid complex or ambiguous words
• Memorability: Should stick in listeners' minds after hearing once

CONTENT GUIDELINES:
• Be specific to the niche/topic but not overly narrow
• Avoid generic podcast terms: "Show", "Podcast", "Cast", "Talk", "Radio"
• Use active, engaging language that creates curiosity
• Consider emotional resonance with target audience
• Ensure cultural sensitivity and broad appeal

TECHNICAL CONSIDERATIONS:
• Check domain availability (.com preferred)
• Ensure social media handle availability
• Avoid trademark conflicts with existing brands
• Consider international pronunciation and meaning
• Test how it sounds in podcast directories

CREATIVE TECHNIQUES:
• Alliteration (but don't force it)
• Metaphors and wordplay (when appropriate)
• Action words that imply movement/progress
• Questions that create curiosity
• Contrasts or unexpected combinations

AVOID:
• Generic descriptors ("The Best", "Ultimate", "Top")
• Overused podcast words ("Chronicles", "Conversations", "Stories")
• Numbers or dates that may become outdated
• Negative or controversial terms
• Overly clever puns that obscure meaning
• Names that are too similar to existing popular podcasts

UNIQUENESS REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;

    if (existingNames.length > 0) {
      qualityInstructions += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${existingNames.map(name => `- ${name}`).join('\n')}
Do not create names that are similar to, variations of, or could be confused with any of the above.`;
    }

    let adaptiveInstructions = '';

    if (preferences.patterns.preferredLength) {
      const lengthMap = {
        'short': '1-2 words (punchy and memorable)',
        'medium': '2-3 words (balanced and brandable)',
        'long': '3-4 words (descriptive but still catchy)'
      };
      adaptiveInstructions += `\nFocus on ${lengthMap[preferences.patterns.preferredLength]}. `;
    }

    if (preferences.patterns.preferredStyle) {
      const styleMap = {
        'descriptive': 'clear, straightforward names that explain the content',
        'creative': 'imaginative, metaphorical, or playful names',
        'professional': 'authoritative, business-focused names',
        'playful': 'fun, energetic, engaging names'
      };
      adaptiveInstructions += `Use ${styleMap[preferences.patterns.preferredStyle] || preferences.patterns.preferredStyle}. `;
    }

    if (preferences.patterns.likedKeywords.length > 0) {
      adaptiveInstructions += `\nIncorporate themes similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
    }

    if (preferences.patterns.dislikedKeywords.length > 0) {
      adaptiveInstructions += `\nAvoid themes like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
    }

    if (preferences.likedNames.length > 0) {
      const examples = preferences.likedNames.slice(-2).map(n => n.name).join('", "');
      adaptiveInstructions += `\nGenerate names with similar appeal to: "${examples}" (but completely different concepts). `;
    }

    return `${basePrompt}${qualityInstructions}${adaptiveInstructions}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  };

  const generateSingleNamePrompt = (input: string, preferences: UserPreferences, count: number = 1): string => {
    const existingNames = [
      ...preferences.likedNames.map(n => n.name.toLowerCase()),
      ...preferences.dislikedNames.map(n => n.name.toLowerCase()),
      ...results.map(r => r.name.toLowerCase())
    ];

    const basePrompt = `Create ${count} unique, high-converting podcast name${count > 1 ? 's' : ''} for: ${input}`;

    let qualityInstructions = `

COMPREHENSIVE PODCAST NAMING GUIDELINES:

CORE PRINCIPLES:
1. MEMORABILITY: Names should be easy to remember, spell, and pronounce
2. BRANDABILITY: Names should work as a brand and be trademarkable
3. SEARCHABILITY: Names should be discoverable and SEO-friendly
4. CLARITY: Names should clearly communicate the podcast's value proposition
5. UNIQUENESS: Names must be completely distinct from existing podcasts

STRUCTURAL REQUIREMENTS:
• Length: 2-4 words maximum (shorter is generally better)
• Pronunciation: Easy to say and understand when spoken aloud
• Spelling: Intuitive spelling, avoid complex or ambiguous words
• Memorability: Should stick in listeners' minds after hearing once

CONTENT GUIDELINES:
• Be specific to the niche/topic but not overly narrow
• Avoid generic podcast terms: "Show", "Podcast", "Cast", "Talk", "Radio"
• Use active, engaging language that creates curiosity
• Consider emotional resonance with target audience
• Ensure cultural sensitivity and broad appeal

UNIQUENESS REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;

    if (existingNames.length > 0) {
      qualityInstructions += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${existingNames.map(name => `- ${name}`).join('\n')}
Do not create names similar to any of the above.`;
    }

    let adaptiveInstructions = '';
    if (preferences.patterns.likedKeywords.length > 0) {
      adaptiveInstructions += `\nIncorporate themes similar to: ${preferences.patterns.likedKeywords.join(', ')}. `;
    }

    if (preferences.patterns.dislikedKeywords.length > 0) {
      adaptiveInstructions += `\nAvoid themes like: ${preferences.patterns.dislikedKeywords.join(', ')}. `;
    }

    return `${basePrompt}${qualityInstructions}${adaptiveInstructions}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${count > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ''}]}`;
  };

  const generatePodcastNames = async (useAdaptivePrompt: boolean = false) => {
    if (!input.trim()) {
      setError('Please describe what your podcast is about');
      return;
    }

    if (!checkUsageLimit()) {
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    setResults([]);

    if (!useAdaptivePrompt) {
      setFeedback([]);
      setShowRefinementButton(false);
      setIsRefining(false);
      setHasProvidedFeedback(false);
    } else {
      setIsRefining(true);
    }

    try {
      const prompt = useAdaptivePrompt
        ? generateAdaptivePrompt(input, preferences)
        : `Create 4 unique, high-converting podcast names for: ${input}

COMPREHENSIVE PODCAST NAMING GUIDELINES:

CORE PRINCIPLES:
1. MEMORABILITY: Names should be easy to remember, spell, and pronounce
2. BRANDABILITY: Names should work as a brand and be trademarkable
3. SEARCHABILITY: Names should be discoverable and SEO-friendly
4. CLARITY: Names should clearly communicate the podcast's value proposition
5. UNIQUENESS: Names must be completely distinct from existing podcasts

STRUCTURAL REQUIREMENTS:
• Length: 2-4 words maximum (shorter is generally better)
• Pronunciation: Easy to say and understand when spoken aloud
• Spelling: Intuitive spelling, avoid complex or ambiguous words
• Memorability: Should stick in listeners' minds after hearing once

CONTENT GUIDELINES:
• Be specific to the niche/topic but not overly narrow
• Avoid generic podcast terms: "Show", "Podcast", "Cast", "Talk", "Radio"
• Use active, engaging language that creates curiosity
• Consider emotional resonance with target audience
• Ensure cultural sensitivity and broad appeal

TECHNICAL CONSIDERATIONS:
• Check domain availability (.com preferred)
• Ensure social media handle availability
• Avoid trademark conflicts with existing brands
• Consider international pronunciation and meaning
• Test how it sounds in podcast directories

CREATIVE TECHNIQUES:
• Alliteration (but don't force it)
• Metaphors and wordplay (when appropriate)
• Action words that imply movement/progress
• Questions that create curiosity
• Contrasts or unexpected combinations

AVOID:
• Generic descriptors ("The Best", "Ultimate", "Top")
• Overused podcast words ("Chronicles", "Conversations", "Stories")
• Numbers or dates that may become outdated
• Negative or controversial terms
• Overly clever puns that obscure meaning
• Names that are too similar to existing popular podcasts

UNIQUENESS REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from API');
      }

      const generatedText = data.candidates[0].content.parts[0].text;

      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in API response');
      }

      const parsedResponse: ApiResponse = JSON.parse(jsonMatch[0]);

      if (!parsedResponse.podcast_names || !Array.isArray(parsedResponse.podcast_names)) {
        throw new Error('Invalid response structure');
      }

      setResults(parsedResponse.podcast_names);
      incrementUsageCount(4);
      checkDomainsForResults(parsedResponse.podcast_names);

      const initialFeedback: NameFeedback[] = parsedResponse.podcast_names.map((name, index) => ({
        name: name.name,
        description: name.description,
        liked: null,
        timestamp: Date.now(),
        index
      }));
      setFeedback(initialFeedback);

    } catch (err) {
      console.error('Error generating podcast names:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setIsRefining(false);
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  // Feedback handling functions
  const handleFeedback = async (index: number, liked: boolean) => {
    const currentName = results[index];
    if (!currentName) return;

    if (liked) {
      const beforeScrollY = window.scrollY;
      const beforeDocumentHeight = document.documentElement.scrollHeight;
      const beforeViewportHeight = window.innerHeight;
      const beforeScrollFromBottom = beforeDocumentHeight - beforeScrollY - beforeViewportHeight;

      setFlyingToFavorites(prev => new Set([...prev, index]));

      preserveScrollPosition(() => {
        setSuccessMessage(`"${currentName.name}" added to favorites!`);
      });
      setTimeout(() => {
        preserveScrollPosition(() => {
          setSuccessMessage(null);
        });
      }, 2000);

      setTimeout(() => {
        const favoritesContainer = document.querySelector('.favorites-section');
        const beforeFavoritesHeight = favoritesContainer ? favoritesContainer.scrollHeight : 0;

        setFavorites(prev => {
          if (prev.find(fav => fav.name === currentName.name)) {
            return prev;
          }
          return [...prev, currentName];
        });

        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            const afterFavoritesHeight = favoritesContainer ? favoritesContainer.scrollHeight : 0;
            const actualHeightIncrease = afterFavoritesHeight - beforeFavoritesHeight;
            const afterDocumentHeight = document.documentElement.scrollHeight;
            const documentHeightIncrease = afterDocumentHeight - beforeDocumentHeight;
            const heightIncrease = actualHeightIncrease > 0 ? actualHeightIncrease : documentHeightIncrease;
            const newScrollY = beforeScrollY + heightIncrease;
            window.scrollTo(0, Math.max(0, newScrollY));
          });
        });
      }, 100);

      setTimeout(() => {
        setFlyingToFavorites(prev => {
          const newSet = new Set(prev);
          newSet.delete(index);
          return newSet;
        });
      }, 700);

      setPreferences(prev => {
        const newPreferences = { ...prev };
        newPreferences.dislikedNames = newPreferences.dislikedNames.filter(n => n.name !== currentName.name);
        if (!newPreferences.likedNames.find(n => n.name === currentName.name)) {
          newPreferences.likedNames.push({
            name: currentName.name,
            description: currentName.description,
            liked: true,
            timestamp: Date.now(),
            index: index
          });
        }
        newPreferences.patterns = analyzePreferences([...newPreferences.likedNames, ...newPreferences.dislikedNames]);
        return newPreferences;
      });

      setPendingReplacements(prev => new Set([...prev, index]));

    } else {
      const currentScrollY = window.scrollY;
      setPendingReplacements(prev => new Set([...prev, index]));

      setPreferences(prev => {
        const newPreferences = { ...prev };
        newPreferences.likedNames = newPreferences.likedNames.filter(n => n.name !== currentName.name);
        if (!newPreferences.dislikedNames.find(n => n.name === currentName.name)) {
          newPreferences.dislikedNames.push({
            name: currentName.name,
            description: currentName.description,
            liked: false,
            timestamp: Date.now(),
            index: index
          });
        }
        newPreferences.patterns = analyzePreferences([...newPreferences.likedNames, ...newPreferences.dislikedNames]);
        return newPreferences;
      });

      setTimeout(() => {
        window.scrollTo(0, currentScrollY);
      }, 0);
    }

    if (input.trim()) {
      generateReplacementSuggestion(index);
    }

    if (!hasProvidedFeedback) {
      setHasProvidedFeedback(true);
    }
  };

  // Generate replacement for a specific index
  const generateReplacementSuggestion = async (replaceIndex: number) => {
    if (!checkUsageLimit()) {
      return;
    }

    try {
      const singleNamePrompt = generateSingleNamePrompt(input, preferences, 1);

      const response = await fetch('https://api.yttranscribe.com/podcastNameGenerator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: singleNamePrompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate replacement suggestion: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error('No content in API response');
      }

      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      const newName = parsedResponse.podcast_names?.[0];

      if (newName) {
        // Initialize the new result with checking status
        setResults(prev => {
          const newResults = [...prev];
          newResults[replaceIndex] = {
            name: newName.name,
            description: newName.description,
            domainStatus: 'checking',
            availableDomains: []
          };
          return newResults;
        });

        setPendingReplacements(prev => {
          const newSet = new Set(prev);
          newSet.delete(replaceIndex);
          return newSet;
        });

        setFeedback(prev => {
          const filtered = prev.filter(f => f.index !== replaceIndex);
          return filtered;
        });

        // Use retry logic for domain checking
        setDomainChecking(prev => new Set([...prev, replaceIndex]));

        try {
          // Generate 4 domain suggestions for this podcast name
          const baseDomain = generateDomainName(newName.name);
          const domainSuggestions = [
            baseDomain,
            `${baseDomain}show`,
            `${baseDomain}cast`,
            `${baseDomain}pod`
          ];

          // Use retry logic to find available domains
          const availableDomains = DOMAIN_RETRY_CONFIG.enableRetryLogic
            ? await checkMultipleDomainsWithRetry(domainSuggestions, newName.name, DOMAIN_RETRY_CONFIG.maxRetries, replaceIndex)
            : await checkMultipleDomainsWithRetry(domainSuggestions, newName.name, 0, replaceIndex);

          // Update the result with available domains
          setResults(prev => {
            const newResults = [...prev];
            if (newResults[replaceIndex]) {
              newResults[replaceIndex].availableDomains = availableDomains;
              newResults[replaceIndex].domainStatus = availableDomains.length > 0 ? 'available' : 'taken';
              newResults[replaceIndex].suggestedDomain = availableDomains.length > 0 ? availableDomains[0] : baseDomain;
            }
            return newResults;
          });

        } catch (error) {
          console.error(`Error checking domains for replacement "${newName.name}":`, error);
          setResults(prev => {
            const newResults = [...prev];
            if (newResults[replaceIndex]) {
              newResults[replaceIndex].domainStatus = 'error';
              newResults[replaceIndex].availableDomains = [];
            }
            return newResults;
          });
        } finally {
          setDomainChecking(prev => {
            const newSet = new Set(prev);
            newSet.delete(replaceIndex);
            return newSet;
          });
        }
      } else {
        throw new Error('No new name found in API response');
      }

    } catch (error) {
      console.error(`Error generating replacement suggestion for index ${replaceIndex}:`, error);
      setPendingReplacements(prev => {
        const newSet = new Set(prev);
        newSet.delete(replaceIndex);
        return newSet;
      });
      setFeedback(prev => {
        const filtered = prev.filter(f => f.index !== replaceIndex);
        return filtered;
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generatePodcastNames();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      generatePodcastNames();
    }
  };

  const styles = `
        .podcast-name-generator {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
          max-width: 920px;
          margin: 0 auto;
          padding: 20px;
          background: #ffffff;
          border-radius: 16px;
          box-sizing: border-box;
        }

        .generator-container {
          width: 100%;
          box-sizing: border-box;
        }

        .header-section {
          text-align: center;
          margin-bottom: 32px;
        }

        .main-title {
          font-size: 3rem;
          font-weight: 800;
          color: #1a1a1a;
          margin: 0 0 16px 0;
          background: linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .main-subtitle {
          font-size: 1.5rem;
          color: #4a5568;
          margin: 0 0 32px 0;
          font-weight: 500;
          line-height: 1.4;
        }

        .benefits-section {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 48px;
          margin: 0 0 48px 0;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .benefit-checkmark {
          width: 24px;
          height: 24px;
          background-color: #6941C7;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .benefit-text {
          color: #2d3748;
          font-size: 1rem;
          font-weight: 500;
          white-space: nowrap;
        }

        .limit-reached-banner {
          margin-bottom: 24px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border: 1px solid #f87171;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(248, 113, 113, 0.1);
          text-align: center;
        }

        .limit-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .limit-icon {
          font-size: 2.5rem;
          margin-bottom: 8px;
        }

        .limit-text p {
          margin: 0;
          color: #991b1b;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        .initial-input-section {
          margin-bottom: 32px;
          padding: 26px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 2px solid #e2e8f0;
          border-radius: 20px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
          text-align: center;
          box-sizing: border-box;
        }

        .input-form {
          margin-bottom: 32px;
        }

        .input-container {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .button-social-container {
          display: flex;
          align-items: center;
          gap: 24px;
          justify-content: space-between;
          flex-direction: row-reverse;
        }

        .input-field {
          width: 100%;
          padding: 16px 20px;
          font-size: 1rem;
          border: 2px solid #e1e5e9;
          border-radius: 12px;
          resize: vertical;
          min-height: 80px;
          font-family: inherit;
          transition: all 0.2s ease;
          box-sizing: border-box;
        }

        .input-field:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-field:disabled {
          background-color: #f8f9fa;
          cursor: not-allowed;
        }

        .generate-button {
          align-self: flex-start;
          padding: 14px 28px;
          font-size: 1rem;
          font-weight: 600;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 160px;
        }

        .generate-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .generate-button.disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          cursor: not-allowed !important;
          opacity: 0.7;
        }

        .error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 12px;
          color: #dc2626;
          font-weight: 500;
          margin-bottom: 24px;
        }

        .error-icon {
          font-size: 1.2rem;
        }

        .success-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border: 1px solid #7dd3fc;
          border-radius: 12px;
          color: #0369a1;
          font-weight: 500;
          margin-bottom: 24px;
          animation: slideInFromTop 0.3s ease-out;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .success-icon {
          font-size: 1.2rem;
        }

        @keyframes slideInFromTop {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .loading-container {
          text-align: center;
          padding: 40px 20px;
          color: #666;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .results-container {
          margin-top: 32px;
          padding: 0 26px;
        }

        .favorites-section {
          margin-bottom: 32px;
          padding: 24px;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
          border: 2px solid #10b981;
          border-radius: 16px;
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
          position: relative;
          overflow: hidden;
        }

        .favorites-section::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
          animation: celebrate 3s ease-in-out infinite;
          pointer-events: none;
        }

        @keyframes celebrate {
          0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
          50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .favorites-header h3 {
          margin: 0 0 12px 0;
          color: #065f46;
          font-size: 1.5rem;
          font-weight: 800;
          text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .favorites-subtitle {
          margin: 0 0 20px 0;
          color: #047857;
          font-size: 1rem;
          line-height: 1.5;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.7);
          padding: 12px 16px;
          border-radius: 8px;
          border-left: 4px solid #10b981;
        }

        .favorites-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
        }

        .favorite-card {
          background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
          border: 2px solid #10b981;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .favorite-card::before {
          content: '✨';
          position: absolute;
          top: 12px;
          right: 12px;
          font-size: 1.2rem;
          opacity: 0.6;
        }

        .favorite-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
          border-color: #059669;
        }

        .favorite-content {
          flex: 1;
          margin-bottom: 16px;
        }

        .favorite-name {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-description {
          margin: 0 0 12px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }

        .input-section-simple {
          margin-bottom: 32px;
        }

        .input-help-message-simple {
          margin-bottom: 16px;
          text-align: center;
        }

        .input-sub-description {
          margin: 0;
          color: #075985;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        .suggestions-section {
          margin-bottom: 24px;
        }

        .suggestions-header h3 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .onboarding-banner {
          margin: 20px 0;
          padding: 16px 20px;
          background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
          border: 2px solid #0ea5e9;
          border-radius: 12px;
          animation: slideInDown 0.5s ease-out;
        }

        .onboarding-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .onboarding-icon {
          font-size: 1.5rem;
          flex-shrink: 0;
        }

        .onboarding-text {
          color: #0c4a6e;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        @keyframes slideInDown {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .results-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .result-card {
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 16px;
          padding: 20px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .result-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .result-card.pending {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-color: #cbd5e0;
        }

        .result-card.pending .result-name {
          color: #64748b;
          font-style: italic;
        }

        .result-card.pending .result-description {
          color: #64748b;
          font-style: italic;
        }

        .result-card.liked {
          border-color: #10b981;
          background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
        }

        .result-card.disliked {
          border-color: #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
          opacity: 0.8;
          transform: scale(0.98);
        }

        @keyframes flyToFavorites {
          0% {
            transform: scale(1) translateY(0) translateX(0);
            opacity: 1;
            z-index: 1000;
          }
          15% {
            transform: scale(0.95) translateY(-10px) translateX(0);
            opacity: 0.9;
          }
          50% {
            transform: scale(0.8) translateY(-100px) translateX(-20px);
            opacity: 0.7;
          }
          85% {
            transform: scale(0.6) translateY(-200px) translateX(-40px);
            opacity: 0.4;
          }
          100% {
            transform: scale(0.4) translateY(-300px) translateX(-60px);
            opacity: 0;
          }
        }

        .result-card.flying-to-favorites {
          animation: flyToFavorites 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          pointer-events: none;
          position: relative;
          z-index: 1000;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .result-card.flying-to-favorites::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #6941c7 0%, #8b5cf6 100%);
          border-radius: 12px;
          opacity: 0.2;
          animation: trailEffect 0.7s ease-out forwards;
          z-index: -1;
        }

        @keyframes trailEffect {
          0% {
            opacity: 0;
            transform: translateY(0);
          }
          20% {
            opacity: 0.3;
            transform: translateY(-20px);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px);
          }
        }

        .result-card.flying-to-favorites::before {
          content: '↗️';
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 16px;
          animation: directionArrow 0.7s ease-out forwards;
          z-index: 1001;
          pointer-events: none;
        }

        @keyframes directionArrow {
          0% {
            opacity: 0;
            transform: translateY(10px) scale(0.8);
          }
          30% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1);
          }
          70% {
            opacity: 0.6;
            transform: translateY(-50px) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px) scale(0.7);
          }
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 16px;
        }

        .result-name {
          margin: 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
          flex: 1;
        }

        .result-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }

        .feedback-buttons {
          display: flex;
          gap: 4px;
        }

        .feedback-button {
          width: 36px;
          height: 36px;
          border: 2px solid #e2e8f0;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          position: relative;
        }

        .feedback-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feedback-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .feedback-button.loading {
          animation: spin 1s linear infinite;
        }

        .like-button:hover:not(:disabled) {
          border-color: #10b981;
          background: #ecfdf5;
        }

        .like-button.active {
          border-color: #10b981;
          background: #10b981;
          color: white;
          transform: scale(1.1);
        }

        .dislike-button:hover:not(:disabled) {
          border-color: #ef4444;
          background: #fef2f2;
        }

        .dislike-button.active {
          border-color: #ef4444;
          background: #ef4444;
          color: white;
          transform: scale(1.1);
        }

        .copy-button {
          padding: 8px 12px;
          font-size: 0.85rem;
          font-weight: 500;
          color: #4b5563;
          background: #f9fafb;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .copy-button:hover:not(:disabled) {
          background: #f3f4f6;
          border-color: #9ca3af;
          transform: translateY(-1px);
        }

        .copy-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .copy-button.small {
          padding: 6px 10px;
          font-size: 0.8rem;
        }

        .result-description {
          margin: 0 0 16px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .domain-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          font-size: 0.85rem;
          margin-top: 12px;
        }

        .domain-info.inline {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 6px;
        }

        .domain-label {
          color: #64748b;
          font-weight: 500;
        }

        .domain-name {
          color: #1e293b;
          font-weight: 600;
        }

        .domain-text {
          background: #e2e8f0;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.8rem;
          color: #1e293b;
        }

        .available-domains {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          align-items: center;
        }

        .domain-status {
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .domain-status.checking {
          color: #0369a1;
          background: #e0f2fe;
        }

        .domain-status.available {
          color: #065f46;
          background: #d1fae5;
        }

        .domain-status.taken {
          color: #991b1b;
          background: #fee2e2;
        }

        .domain-status.error {
          color: #92400e;
          background: #fef3c7;
        }

        /* Social Proof Section */
        .social-proof {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 0;
          margin: 0;
        }

        .user-avatars {
          display: flex;
          margin-right: 8px;
        }

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid rgba(105, 65, 199, 0.3);
          margin-left: -8px;
          overflow: hidden;
          background: white;
        }

        .avatar:first-child {
          margin-left: 0;
        }

        .avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .rating-section {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }

        .stars {
          display: flex;
          gap: 2px;
        }

        .star {
          width: 16px;
          height: 16px;
        }

        .trust-text {
          color: #4a5568;
          font-size: 0.9rem;
          font-weight: 500;
          white-space: nowrap;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .podcast-name-generator {
            padding: 16px;
            margin: 0 16px;
          }

          .main-title {
            font-size: 2.2rem;
          }

          .main-subtitle {
            font-size: 1.2rem;
          }

          .benefits-section {
            flex-direction: column;
            gap: 16px;
            align-items: center;
          }

          .benefit-item {
            justify-content: center;
          }

          .initial-input-section {
            padding: 20px;
          }

          .button-social-container {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
          }

          .generate-button {
            align-self: stretch;
            text-align: center;
          }

          .results-container {
            padding: 0 16px;
          }

          .results-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .favorites-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .result-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
          }

          .result-actions {
            align-self: stretch;
            justify-content: space-between;
          }

          .domain-info.inline {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }

        @media (max-width: 480px) {
          .main-title {
            font-size: 1.8rem;
          }

          .main-subtitle {
            font-size: 1rem;
          }

          .benefit-text {
            font-size: 0.9rem;
          }

          .social-proof {
            flex-direction: column;
            gap: 12px;
          }

          .user-avatars {
            margin-right: 0;
            margin-bottom: 8px;
          }

          .initial-input-section {
            padding: 16px;
          }

          .favorites-section {
            padding: 16px;
          }

          .result-card {
            padding: 16px;
          }
        }
      `;

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className={`podcast-name-generator ${className}`} style={style}>
        <div className="generator-container">
          {/* Permanent Header Section */}
          <div className="header-section">
            <h1 className="main-title">Free Podcast Name Generator</h1>
            <h2 className="main-subtitle">Create the Perfect Name for Your Podcast in Seconds</h2>
          </div>

          {/* Benefits Section - Removed checkmarks per user preference */}
          <div className="benefits-section">
            <div className="benefit-item">
              <span className="benefit-text">100% Free Forever</span>
            </div>
            <div className="benefit-item">
              <span className="benefit-text">No Sign-up Required</span>
            </div>
            <div className="benefit-item">
              <span className="benefit-text">Instant Results</span>
            </div>
          </div>

          {/* Usage Limit Reached Message */}
          {isLimitReached && (
            <div className="limit-reached-banner">
              <div className="limit-content">
                <span className="limit-icon">⚠️</span>
                <div className="limit-text">
                  <p>You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below.</p>
                </div>
              </div>
            </div>
          )}

          {/* Initial Input Section - Always visible when no results */}
          {results.length === 0 && (
            <div className="initial-input-section">
              <form onSubmit={handleSubmit} className="input-form">
                <div className="input-container">
                  <textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Describe what your podcast is about"
                    className="input-field"
                    rows={3}
                    disabled={loading}
                  />
                  <div className="button-social-container">
                    <button
                      type="submit"
                      disabled={loading || !input.trim() || isLimitReached}
                      className={`generate-button ${isLimitReached ? 'disabled' : ''}`}
                    >
                      {loading ? 'Generating...' :
                       isLimitReached ? 'Daily Limit Reached' :
                       'Generate Names'}
                    </button>

                    {/* Social Proof Section - Inline with button */}
                    <div className="social-proof">
                      <div className="user-avatars">
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/women/63.jpg" alt="User avatar" />
                        </div>
                        <div className="avatar">
                          <img src="https://randomuser.me/api/portraits/men/54.jpg" alt="User avatar" />
                        </div>
                      </div>
                      <div className="rating-section">
                        <div className="stars">
                          <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                          <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                          </svg>
                        </div>
                        <span className="trust-text">Trusted by 12k+ users</span>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          )}

          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          {successMessage && (
            <div className="success-message">
              <span className="success-icon">✨</span>
              {successMessage}
            </div>
          )}

          {loading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>{isRefining ? 'Generating better names based on your preferences...' : 'Generating creative podcast names...'}</p>
            </div>
          )}

          {results.length > 0 && (
            <div className="results-container">
              {/* Favorites Section - Show at top when they exist */}
              {favorites.length > 0 && (
                <div className="favorites-section">
                  <div className="favorites-header">
                    <h3>🏆 Your Winning Podcast Names ({favorites.length})</h3>
                    <p className="favorites-subtitle">Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!</p>
                  </div>
                  <div className="favorites-grid">
                    {favorites.map((favorite, index) => (
                      <div key={`fav-${index}`} className="favorite-card">
                        <div className="favorite-content">
                          <h4 className="favorite-name">{favorite.name}</h4>
                          <p className="favorite-description">{favorite.description}</p>
                          {/* Available domains display for favorites */}
                          {favorite.availableDomains && favorite.availableDomains.length > 0 && (
                            <div className="domain-info inline">
                              <span className="domain-label">Available domains:</span>
                              <div className="available-domains">
                                {favorite.availableDomains.slice(0, 4).map((domain, idx) => (
                                  <code key={idx} className="domain-text">{domain}.com</code>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Fallback for favorites without available domains */}
                          {favorite.suggestedDomain && (!favorite.availableDomains || favorite.availableDomains.length === 0) && (
                            <div className="domain-info inline">
                              <span className="domain-label">Domain:</span>
                              <span className="domain-name">{favorite.suggestedDomain}.com</span>
                              <span className={`domain-status ${favorite.domainStatus}`}>
                                {favorite.domainStatus === 'taken' ? '❌ Taken' :
                                 favorite.domainStatus === 'error' ? '⚠️ Check manually' : '🔍 Checking...'}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="favorite-actions">
                          <button
                            onClick={() => copyToClipboard(favorite.name, -1)}
                            className="copy-button small"
                            title="Copy to clipboard"
                          >
                            📋 Copy
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Input Section with Helpful Messaging */}
              <div className="input-section-simple">
                <div className="input-help-message-simple">
                  <p className="input-sub-description">💡 Want different suggestions? Update your description below - <strong>your favorites will stay safe!</strong></p>
                </div>

                <form onSubmit={handleSubmit} className="input-form">
                  <div className="input-container">
                    <textarea
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Describe what your podcast is about"
                      className="input-field"
                      rows={3}
                      disabled={loading}
                    />
                    <div className="button-social-container">
                      <button
                        type="submit"
                        disabled={loading || !input.trim() || isLimitReached}
                        className={`generate-button ${isLimitReached ? 'disabled' : ''}`}
                      >
                        {loading ? 'Generating...' :
                         isLimitReached ? 'Daily Limit Reached' :
                         'Generate Names'}
                      </button>

                      {/* Social Proof Section - Inline with button */}
                      <div className="social-proof">
                        <div className="user-avatars">
                          <div className="avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" />
                          </div>
                          <div className="avatar">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" />
                          </div>
                          <div className="avatar">
                            <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="User avatar" />
                          </div>
                          <div className="avatar">
                            <img src="https://randomuser.me/api/portraits/women/63.jpg" alt="User avatar" />
                          </div>
                          <div className="avatar">
                            <img src="https://randomuser.me/api/portraits/men/54.jpg" alt="User avatar" />
                          </div>
                        </div>
                        <div className="rating-section">
                          <div className="stars">
                            <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                              <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                            </svg>
                            <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                              <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                            </svg>
                            <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                              <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                            </svg>
                            <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                              <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                            </svg>
                            <svg className="star" viewBox="0 0 24 24" fill="#fbbf24">
                              <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd"></path>
                            </svg>
                          </div>
                          <span className="trust-text">Trusted by 12k+ users</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>

              {/* Current Suggestions */}
              <div className="suggestions-section">
                <div className="suggestions-header">
                  <h3>🎯 Current Suggestions</h3>
                </div>

                {/* AI Learning Info Box */}
                <div className="onboarding-banner">
                  <div className="onboarding-content">
                    <span className="onboarding-icon">💡</span>
                    <div className="onboarding-text">
                      <strong>Smart AI Learning:</strong> The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste.
                    </div>
                  </div>
                </div>
                <div className="results-grid">
                  {results.map((result, index) => {
                    const feedbackItem = feedback.find(f => f.index === index);
                    const isLiked = feedbackItem?.liked === true;
                    const isDisliked = feedbackItem?.liked === false;
                    const isPendingReplacement = pendingReplacements.has(index);
                    const isGeneratingReplacement = domainChecking.has(index);

                    return (
                      <div
                        key={index}
                        className={`result-card ${isLiked ? 'liked' : ''} ${isDisliked ? 'disliked' : ''} ${isPendingReplacement ? 'pending' : ''} ${flyingToFavorites.has(index) ? 'flying-to-favorites' : ''}`}
                        style={{
                          opacity: isPendingReplacement ? 0.6 : 1,
                          pointerEvents: isPendingReplacement ? 'none' : 'auto'
                        }}
                      >
                        <div className="result-header">
                          <h4 className="result-name">
                            {isPendingReplacement ?
                              (isLiked ? 'Generating new suggestion...' : 'Generating better suggestion...') :
                              result.name}
                          </h4>
                          <div className="result-actions">
                            <div className="feedback-buttons">
                              <button
                                onClick={() => handleFeedback(index, true)}
                                className={`feedback-button like-button ${isLiked ? 'active' : ''}`}
                                title="I like this name"
                                disabled={isPendingReplacement}
                              >
                                👍
                              </button>
                              <button
                                onClick={() => handleFeedback(index, false)}
                                className={`feedback-button dislike-button ${isDisliked ? 'active' : ''} ${isPendingReplacement ? 'loading' : ''}`}
                                title={isPendingReplacement ? "Generating replacement..." : "I don't like this name"}
                                disabled={isPendingReplacement}
                              >
                                {isPendingReplacement ? '🔄' : '👎'}
                              </button>
                            </div>
                            <button
                              onClick={() => copyToClipboard(result.name, index)}
                              className="copy-button"
                              title="Copy podcast name"
                              disabled={isPendingReplacement}
                            >
                              {copiedIndex === index ? '✓ Copied!' : '📋 Copy'}
                            </button>
                          </div>
                        </div>
                        <p className="result-description">
                          {isPendingReplacement ?
                            (isLiked ? 'Added to favorites! Generating a new suggestion...' : 'Creating a better suggestion based on your preferences...') :
                            result.description}
                        </p>

                        {/* Domain retry status */}
                        {domainRetryStatus.has(index) && (
                          <div className="domain-info inline">
                            <span className="domain-label">🔄 {domainRetryStatus.get(index)}</span>
                          </div>
                        )}

                        {/* Available domains display */}
                        {result.availableDomains && result.availableDomains.length > 0 && !isGeneratingReplacement && (
                          <div className="domain-info inline">
                            <span className="domain-label">Available domains:</span>
                            <div className="available-domains">
                              {result.availableDomains.slice(0, 4).map((domain, idx) => (
                                <code key={idx} className="domain-text">{domain}.com</code>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Domain checking status */}
                        {(result.domainStatus === 'checking' || domainChecking.has(index)) && !domainRetryStatus.has(index) && (
                          <div className="domain-info inline">
                            <span className="domain-label">⏳ Checking domains...</span>
                          </div>
                        )}

                        {/* No domains found */}
                        {result.domainStatus === 'taken' && (!result.availableDomains || result.availableDomains.length === 0) && !isGeneratingReplacement && (
                          <div className="domain-info inline">
                            <span className="domain-label">❌ No available domains found</span>
                          </div>
                        )}

                        {/* Domain check error */}
                        {result.domainStatus === 'error' && !isGeneratingReplacement && (
                          <div className="domain-info inline">
                            <span className="domain-label">⚠️ Domain check failed</span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}